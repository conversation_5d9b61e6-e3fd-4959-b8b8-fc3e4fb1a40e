這裡是開發需求筆記，主要用途是幫助 AI 紀錄開發過程，方便下一次能快速了解專案內容。
=====================

# 專案架構

## 主要檔案結構
```
bank_login/
├── main.py              # 程式入口點
├── build.py            # 專案建置腳本
├── .env.example        # 環境變數範例
├── version.txt         # 版本資訊
├── bank/              # 銀行操作模組
│   ├── __init__.py    # 模組入口點
│   ├── banks.py       # 銀行資訊管理
│   ├── base.py        # 基礎類別 (Selenium 版本)
│   ├── base_playwright.py  # 基礎類別 (Playwright 版本)
│   ├── yusan.py       # 玉山銀行實作 (Selenium 版本)
│   ├── yusan_playwright.py # 玉山銀行實作 (Playwright 版本)
│   ├── taixin.py      # 台新銀行實作 (Selenium 版本)
│   ├── taixin_playwright.py # 台新銀行實作 (Playwright 版本)
│   ├── cathaybk.py    # 國泰世華銀行實作 (Selenium 版本)
│   ├── cathaybk_playwright.py # 國泰世華銀行實作 (Playwright 版本)
│   ├── manager.py     # 銀行管理器 (Selenium 版本)
│   └── manager_playwright.py # 銀行管理器 (Playwright 版本)
├── gui/                # GUI 相關模組
│   ├── app.py         # 主應用程式視窗
│   ├── components/    # UI 元件
│   │   ├── login.py      # 登入介面
│   │   ├── devices.py    # 設備管理介面
│   │   ├── transactions.py # 交易記錄介面
│   │   ├── logs.py       # 日誌介面
│   │   └── dialogs/      # 對話框元件
│   │       └── add_device.py # 新增設備對話框
│   ├── managers/     # 管理器
│   │   └── device_manager.py # 設備管理器
│   ├── styles/       # UI 樣式
│   └── utils/        # 工具類
```

## 環境配置
專案使用 .env 檔案進行配置管理：
- API_BASE_URL：API 服務的基礎 URL
- 開發時需複製 .env.example 為 .env 並設定適當的值

## 建置需求

### 必要檔案
- .env 配置檔案
- requirements.txt 依賴清單
- fonts 字體目錄
- gui 目錄
- version.txt 版本資訊檔案

### 依賴套件
- ttkbootstrap >= 1.10.1：現代化 GUI 元件
- requests >= 2.31.0：HTTP 請求處理
- python-dotenv >= 1.0.0：環境變數管理
- selenium >= 4.15.2：瀏覽器自動化 (Selenium 版本)
- undetected-chromedriver >= 3.5.3：反檢測的 Chrome 驅動 (Selenium 版本)
- playwright >= 1.40.0：現代化瀏覽器自動化 (Playwright 版本)
- pyperclip >= 1.8.2：剪貼簿操作
- pillow >= 10.1.0：圖片處理

## 建置流程 (build.py)
1. 環境檢查
   - 驗證必要檔案存在
   - 檢查字體和 GUI 目錄

2. 清理流程
   - 清理舊的建置檔案（dist、build、release）
   - 安全的檔案刪除機制

3. 打包流程
   - 安裝依賴套件
   - 使用 PyInstaller 打包
   - 設定隱藏 imports
   - 打包資源文件（字體、配置等）

4. 發布流程
   - 建立 release 目錄
   - 複製執行檔
   - 清理臨時檔案

## 程式流程

1. 程式啟動 (main.py)
   - 檢查必要的字體文件
   - 初始化主應用程式視窗

2. 登入流程 (gui/components/login.py)
   - 顯示登入介面
   - 驗證用戶憑證
   - 成功後獲取 token 並進入主介面

3. 主介面 (gui/app.py)
   - 包含三個主要頁面：設備管理、交易記錄、日誌
   - 使用 Notebook 進行頁面管理

4. 設備管理 (gui/components/devices.py)
   - 顯示設備列表（設備名稱、銀行、帳號、狀態等）
   - 提供新增設備功能
   - 支援設備操作（登入、停止）
   - 自動刷新功能

# 主要元件說明

## BankLoginApp (gui/app.py)
- 主應用程式視窗
- 管理整體介面佈局
- 處理登入狀態和頁面切換

## LoginFrame (gui/components/login.py)
- 處理用戶登入
- 包含帳號密碼輸入
- 處理登入請求和回應

## DevicesFrame (gui/components/devices.py)
主要功能：
- 設備列表顯示和管理
- 支援新增設備
- 設備狀態監控
- 自動刷新機制
- 設備操作控制（登入/停止）

特點：
- 使用 TreeView 顯示設備列表
- 支援自動刷新（可配置間隔時間）
- 異步加載設備數據
- 整合日誌系統

## 銀行操作模組 (bank/)

### banks.py
銀行資訊管理模組：
- BankInfo 資料類別：定義銀行基本資訊（代號、名稱、模組）
- SUPPORTED_BANKS：支援的銀行列表
- BankRegistry：銀行註冊管理器
  * 提供銀行代號和名稱的映射
  * 支援動態載入銀行列表
  * 方便新增銀行支援

### BankBase 類
#### base.py (Selenium 版本)
基礎銀行操作類別，提供共用功能：
- 瀏覽器驅動設置
- Cookie 管理
- 安全點擊操作
- 狀態更新機制

#### base_playwright.py (Playwright 版本)
基礎銀行操作類別，提供共用功能：
- 瀏覽器設置與模擬：
  * 手機設備模擬
  * 自定義 User-Agent
  * 瀏覽器參數配置
- Cookie 管理：
  * 自動更新機制
  * API 整合
- 請求監聽和處理：
  * 網路請求攔截
  * 回應分析
- 狀態更新機制：
  * 即時狀態回報
  * 錯誤狀態處理
- 截圖管理：
  * 自動截圖
  * 錯誤時截圖
  * 檔案管理
- 錯誤處理：
  * 異常捕獲
  * 日誌記錄
  * 自動重試

### YusanBank 類
#### yusan.py (Selenium 版本)
玉山銀行特定實作：
- 使用手機版網頁介面
- 自動化操作流程：
  * 存款總覽查詢
  * 定存解約與變更
  * 循環式操作保持登入狀態

#### yusan_playwright.py (Playwright 版本)
玉山銀行特定實作：
- 使用手機版網頁介面
- 自動化操作流程：
  * 存款總覽查詢
  * 定存解約與變更
  * 循環式操作保持登入狀態
- 使用 Playwright 的請求監聽功能：
  * Cookie 自動更新
  * Session 維護
- 元素定位與操作：
  * 使用 role 定位
  * 等待機制
  * 自動重試

### TaixinBank 類
#### taixin.py (Selenium 版本)
台新銀行特定實作：
- 網頁版操作
- iframe 處理
- 效能日誌分析獲取帳號
- Session 管理

#### taixin_playwright.py (Playwright 版本)
台新銀行特定實作：
- 網頁版操作
- 自動化 iframe 處理：
  * 框架切換
  * 元素定位
- 使用 Playwright 的請求監聽功能：
  * 自動獲取帳號
  * Session 管理
- 錯誤處理：
  * 框架切換錯誤處理
  * 元素等待超時處理

### CathayBank 類
#### cathaybk.py (Selenium 版本)
國泰世華銀行特定實作：
- 網頁版操作
- 效能日誌分析：
  * 檢查登入狀態
  * 獲取 Authorization token
- 自動化操作流程：
  * 帳號連結定位與點擊
  * Token 自動更新
  * 循環式操作保持登入狀態
- 錯誤處理：
  * 狀態追踪
  * 自動重試機制
  * 詳細的日誌記錄

#### cathaybk_playwright.py (Playwright 版本)
國泰世華銀行特定實作：
- 手機版網頁操作
- 登入檢測流程：
  * 檢查並點擊"我的帳戶總覽"
  * 自動驗證帳號
  * 點擊進入明細頁面
- 自動化操作：
  * 請求監聽獲取 token：
    - 監聽 API 請求
    - 提取 Authorization token
    - 解析 customerId
  * 自動更新 token：
    - 定期檢查更新
    - API 整合
  * 定期重整保持登入：
    - 60 秒間隔重整
    - 自動重新進入明細頁面
- 錯誤處理：
  * 完整的錯誤捕獲
  * 自動截圖記錄
  * 狀態追踪和回報
- 特點：
  * 使用 role 選擇器定位元素
  * 自動等待頁面載入
  * 請求監聽和處理
  * 完整的錯誤處理機制

### BankManager 類
#### manager.py (Selenium 版本)
統一管理銀行操作：
- 多銀行支援
- 使用 bank_registry 管理銀行資訊
- 線程管理
- 狀態回調機制

#### manager_playwright.py (Playwright 版本)
統一管理銀行操作：
- 多銀行支援：
  * 動態載入銀行模組
  * 版本選擇（Selenium/Playwright）
- 使用 bank_registry 管理銀行資訊：
  * 銀行資訊註冊
  * 模組載入管理
- Playwright 特定的線程管理：
  * 瀏覽器實例管理
  * 資源釋放控制
- 狀態回調機制：
  * 即時狀態更新
  * 錯誤狀態處理

## 工具類
- config_manager: 配置管理
- log_manager: 日誌管理
- thread_manager: 線程管理
- bank_login_manager: 銀行登入管理
- async_utils: 異步操作工具

# 關鍵功能

1. 設備管理
   - 新增設備（支援動態載入銀行列表）
   - 查看設備狀態
   - 設備操作（登入/停止）
   - 自動刷新監控

2. 銀行登入自動化
   - 支援銀行：
     * 玉山銀行：手機版網頁介面
     * 台新銀行：網頁版操作
     * 國泰世華銀行：網頁版操作
     * 台灣銀行：網頁版操作
   - 自動化登入流程
   - Cookie 獲取與更新
   - 狀態監控和錯誤處理
   - 多線程操作
   - 安全退出機制

3. 日誌系統
   - 整合式日誌記錄
   - 支援按設備篩選
   - 即時更新
   - 檔案管理機制：
     * 每次啟動建立新的日誌檔案（包含時間戳）
     * 自動檔案大小限制（10MB）
     * 達到大小限制時自動建立新檔案
     * 保留天數控制
   - GUI 介面：
     * 設備和日誌級別過濾
     * 即時顯示當前日誌
     * 自動跟隨最新日誌檔案
     * 自動刷新和滾動控制

# 技術特點

1. GUI 框架
   - 使用 tkinter 和 ttkbootstrap
   - 現代化的 UI 設計
   - 響應式佈局

2. 自動化技術
- Selenium WebDriver：
  * 瀏覽器自動化操作
  * 效能日誌分析
  * 異常處理機制
  * JavaScript 執行
  * 元素等待機制

- Playwright：
  * 現代化的瀏覽器自動化
  * 內建的移動設備模擬
  * 網路請求監聽和攔截
  * 更穩定的元素定位：
    - Role-based 選擇器
    - 自動等待機制
    - 框架處理
  * 效能優化：
    - 資源控制
    - 並行處理
  * 完整的錯誤處理：
    - 自動重試
    - 截圖記錄
    - 狀態追踪

3. 異步處理
   - 使用線程處理耗時操作
   - 避免 UI 凍結
   - 支援取消操作

4. 錯誤處理
   - 完整的錯誤捕獲和處理機制
   - 用戶友好的錯誤提示
   - 詳細的日誌記錄

5. 配置管理
   - 集中式配置
   - 支援多銀行配置
   - 靈活的 API 設置

6. 建置與打包
   - PyInstaller 單檔打包
   - 資源文件整合
   - 自動依賴管理
   - 安全的建置流程

# 安全性考慮

1. 瀏覽器安全
   - 禁用自動化特徵
   - 自定義 User-Agent
   - 清理會話數據

2. 操作安全
   - 重試機制
   - 超時控制
   - 安全退出處理

3. 數據安全
   - Cookie 加密傳輸
   - 敏感信息保護
   - 會話管理

4. 建置安全
   - 建置過程錯誤處理
   - 檔案安全刪除
   - 臨時檔案清理

# 開發設置

1. 環境準備
   - 複製 .env.example 為 .env
   - 設定 API_BASE_URL
   - 安裝依賴套件：`pip install -r requirements.txt`

2. 開發流程
   - 確保所有必要檔案存在
   - 設定正確的環境變數
   - 運行開發伺服器：`python main.py`

3. 建置發布
   - 確認環境配置正確
   - 執行建置腳本：`python build.py`
   - 檢查 release 目錄中的執行檔

## TransactionsFrame (gui/components/transactions.py)
主要功能：
- 交易記錄查詢和顯示
- 多維度篩選：
  * 設備選擇（支援全部設備）
  * 時間範圍（今日、昨日、本周、上周、本月、上月）
- 資料展示：
  * 使用 TreeView 顯示交易記錄
  * 自動格式化金額顯示
  * 分頁顯示功能
- 統計功能：
  * 當前頁統計（總金額、手續費、單筆手續費）
  * 全部資料統計
- 異步加載：
  * 使用 async_utils 處理資料請求
  * 載入狀態指示
  * 錯誤處理機制

請一定要完全看過
bank\banks.py
bank\base_playwright.py
bank\cathaybk_playwright.py
bank\firstbank_playwright.py
bank\linebank_playwright.py
bank\manager_playwright.py
bank\megabank_playwright.py
bank\post_playwright.py
bank\rakutenbank_playwright.py
bank\taixin_playwright.py
bank\yusan_playwright.py
請一定要看過這些檔案之後，再開始看下面的需求，幫我增加新的銀行模組
1. 每個頁面都不要有超時問題
2. 比對帳號如果錯誤，就要關閉執行緒


富邦銀行流程
瀏覽器要用電腦版開啟

登入網址: https://ebank.taipeifubon.com.tw/B2C/common/Index.faces

進入頁面後，需要先點擊 
page.locator("#frame1").content_frame.get_by_role("link", name="登入").click()

才會跳出登入的輸入視窗

```
<table width="100%" border="0" cellspacing="0" cellpadding="0" class="login_tb">
                       <tbody><tr>
                          <td style="width: 40%" class="hd">身分證字號</td>
                          <td><input id="m1_FPHBPNENOS" name="FPHBPNENOS" type="text" class="input_txt" maxlength="10" autocomplete="off"></td>
                       </tr>
                       <tr>
                          <td class="hd">使用者代碼</td>
                          <td style="white-space: nowrap;">
							<input id="m1_DGWVPLELGZ" name="DGWVPLELGZ" type="password" class="input_txt eye-padding large" maxlength="10" autocomplete="off" data-celebrus-password="true"><a href="javascript:void(0);" tabindex="-1" id="m1_DGWVPLELGZ_eye" class="eye" for="m1_DGWVPLELGZ"></a>
                          	<span id="m1_DGWVPLELGZ_msg"></span>
                          </td>
                       </tr>
                       <tr>
                          <td class="hd">使用者密碼</td>
                          <td>
                            <input id="m1_HJWLOHNYRZ" name="HJWLOHNYRZ" type="password" maxlength="16" class="m1_password input_txt eye-padding large" autocomplete="off" data-celebrus-password="true"><a href="javascript:void(0);" tabindex="-1" id="m1_HJWLOHNYRZ_eye" class="eye" for="m1_HJWLOHNYRZ"></a>
                            <span id="m1_HJWLOHNYRZ_msg"></span>
                            <div class="keyBoard_btn kbtn"></div>
                            <div class="keyBoard_pop kpop" style="display: none;">
                                <div class="tit">動態鍵盤
                                    <div class="close kbtn"></div>
                                </div><script src="/B2C/.ibmjsfres/custom/dynamickeyboard/js/dynamicKeyboard.js?ver=1743167037839" type="text/javascript"></script>
<link rel="stylesheet" href="/B2C/.ibmjsfres/custom/dynamickeyboard/css/dynamicKeyboard.css?ver=1743167037839" type="text/css">
<map name="image-map2"><area shape="rect" coords="0,0,33,33" onclick="addForBoth('0', 'm1_password');return false;" href="#">
<area shape="rect" coords="37,0,70,33" onclick="addForBoth('1', 'm1_password');return false;" href="#">
<area shape="rect" coords="74,0,107,33" onclick="addForBoth('2', 'm1_password');return false;" href="#">
<area shape="rect" coords="111,0,144,33" onclick="addForBoth('3', 'm1_password');return false;" href="#">
<area shape="rect" coords="148,0,181,33" onclick="addForBoth('4', 'm1_password');return false;" href="#">
<area shape="rect" coords="185,0,218,33" onclick="addForBoth('5', 'm1_password');return false;" href="#">
<area shape="rect" coords="222,0,255,33" onclick="addForBoth('6', 'm1_password');return false;" href="#">
<area shape="rect" coords="259,0,292,33" onclick="addForBoth('7', 'm1_password');return false;" href="#">
<area shape="rect" coords="296,0,329,33" onclick="addForBoth('8', 'm1_password');return false;" href="#">
<area shape="rect" coords="333,0,366,33" onclick="addForBoth('9', 'm1_password');return false;" href="#">
<area shape="rect" coords="0,35,33,68" onclick="addForBoth('A', 'm1_password');return false;" href="#">
<area shape="rect" coords="37,35,70,68" onclick="addForBoth('B', 'm1_password');return false;" href="#">
<area shape="rect" coords="74,35,107,68" onclick="addForBoth('C', 'm1_password');return false;" href="#">
<area shape="rect" coords="111,35,144,68" onclick="addForBoth('D', 'm1_password');return false;" href="#">
<area shape="rect" coords="148,35,181,68" onclick="addForBoth('E', 'm1_password');return false;" href="#">
<area shape="rect" coords="185,35,218,68" onclick="addForBoth('F', 'm1_password');return false;" href="#">
<area shape="rect" coords="222,35,255,68" onclick="addForBoth('G', 'm1_password');return false;" href="#">
<area shape="rect" coords="259,35,292,68" onclick="addForBoth('H', 'm1_password');return false;" href="#">
<area shape="rect" coords="296,35,329,68" onclick="addForBoth('I', 'm1_password');return false;" href="#">
<area shape="rect" coords="333,35,366,68" onclick="addForBoth('J', 'm1_password');return false;" href="#">
<area shape="rect" coords="0,70,33,103" onclick="addForBoth('K', 'm1_password');return false;" href="#">
<area shape="rect" coords="37,70,70,103" onclick="addForBoth('L', 'm1_password');return false;" href="#">
<area shape="rect" coords="74,70,107,103" onclick="addForBoth('M', 'm1_password');return false;" href="#">
<area shape="rect" coords="111,70,144,103" onclick="addForBoth('N', 'm1_password');return false;" href="#">
<area shape="rect" coords="148,70,181,103" onclick="addForBoth('O', 'm1_password');return false;" href="#">
<area shape="rect" coords="185,70,218,103" onclick="addForBoth('P', 'm1_password');return false;" href="#">
<area shape="rect" coords="222,70,255,103" onclick="addForBoth('Q', 'm1_password');return false;" href="#">
<area shape="rect" coords="259,70,292,103" onclick="addForBoth('R', 'm1_password');return false;" href="#">
<area shape="rect" coords="296,70,329,103" onclick="addForBoth('S', 'm1_password');return false;" href="#">
<area shape="rect" coords="333,70,366,103" onclick="addForBoth('T', 'm1_password');return false;" href="#">
<area shape="rect" coords="0,105,33,138" onclick="addForBoth('U', 'm1_password');return false;" href="#">
<area shape="rect" coords="37,105,70,138" onclick="addForBoth('V', 'm1_password');return false;" href="#">
<area shape="rect" coords="74,105,107,138" onclick="addForBoth('W', 'm1_password');return false;" href="#">
<area shape="rect" coords="111,105,144,138" onclick="addForBoth('X', 'm1_password');return false;" href="#">
<area shape="rect" coords="148,105,181,138" onclick="addForBoth('Y', 'm1_password');return false;" href="#">
<area shape="rect" coords="185,105,218,138" onclick="addForBoth('Z', 'm1_password');return false;" href="#">
<area shape="rect" coords="222,107,292,139" onclick="clearCurrent('m1_password');return false;" href="#">
<area shape="rect" coords="296,107,365,139" onclick="dynKbConfirm('m1_password');return false;" href="#">
<area shape="rect" coords="458,9,477,33" onclick="closeKB();return false;" href="#">
</map>

<img id="keyboard_m1_password" usemap="#image-map2" src="/B2C/inc/img/mask/spacer.gif">
<script type="text/javascript">$(function(){ $('#keyboard_m1_password').load(function(){resizeHeight(true);});});</script>
<span id="keyboard_m1_password_src" style="display: none;">/B2C/keyboard.jpg?confirmBtn=true&amp;key=m1_password&amp;v=1743519355279</span><script type="text/javascript" '="">    $(document).ready(function doInit() {
        init(true, 'm1_password')
    });</script>
                            </div>
                          </td>
                       </tr>
                       <tr>
							<td class="hd">請輸入右側驗證碼</td>
							<td><input id="m1_userCaptcha" name="m1_userCaptcha" type="text" value="" maxlength="6" size="12" class="input_txt userCaptcha" autocomplete="off">
								<span id="m1_userCaptchaMsg" class="code"><img style="vertical-align:middle;" id="m1_captchaImage" src="/B2C/captchaImage?timestamp=1743519355021"></span>
								
								<a style="vertical-align: bottom;" href="#" onclick="doReGenCode(); return false;" class="link1">重新產生</a>
							</td>
						</tr>
                    </tbody></table>
```

table 的完整 xpath:/html/body/div[3]/div/div/div[2]/div[1]/table

這裡如果有勾選自動登入的功能，就請幫我填入，身分證字號、使用者代碼、使用者密碼

接著持續檢查，如果出現
page.locator("#frame1").content_frame.locator("iframe[name=\"txnFrame\"]").content_frame.get_by_text("狀態：成功")
就代表登入成功，成功登入後，點擊
page.locator("#frame1").content_frame.locator("iframe[name=\"txnFrame\"]").content_frame.locator("#Mp-CDS_01").get_by_role("link", name="我的存款 NTD").click()

然後查找
page.locator("#frame1").content_frame.locator("iframe[name=\"txnFrame\"]").content_frame.get_by_role("link", name="**************")
其中 ************** 是 bank_account

通過帳號檢查後

進行以下點擊
page.locator("#frame1").content_frame.locator("iframe[name=\"txnFrame\"]").content_frame.get_by_role("link", name="快速功能").click()
page.locator("#frame1").content_frame.locator("iframe[name=\"txnFrame\"]").content_frame.get_by_role("link", name="交易明細查詢").click()


接著找到 Cookie 內的所有參數，並且傳送到 update_cookie 的 API
這時候 Cookie 就存入變數


同時也要開始持續監控是否有跳出以下視窗
page.locator("#frame1").content_frame.locator("#timeout_warning").get_by_text("系統訊息")
如果有，就點選
page.locator("#frame1").content_frame.get_by_role("link", name="繼續使用").click()
這個是為了避免被強制登出

然後每五分鐘就將剛剛存入的Cookie變數傳送一次 update_cookie 的 API



todo


我現在希望打包後的客戶端，剛啟動的時候，可以檢察系統是否有安裝 Chocolatey、Node.js、npm、playwright 等等
以及把 mitmdump 需要用到的檔案放到跟目錄中，我們可以直接下載 https://downloads.mitmproxy.org/12.1.1/mitmproxy-12.1.1-windows-x86_64.zip 進行解壓縮

我已經先將相對檔案放在跟目錄下
mitmdump.exe
mitmproxy.exe
mitmweb.exe
請你安排一個適合的目錄來存放這些檔案，並且在啟動的時候進行檢查與安裝安裝

另外以下是 windows 安裝 node.js 的方便指令
# 下載並安裝 Chocolatey：
powershell -c "irm https://community.chocolatey.org/install.ps1|iex"
# 下載並安裝 Node.js：
choco install nodejs-lts --version="22"
# 核對 Node.js 版本：
node -v # 應會印出 "v22.16.0"。
# 核對 npm 版本：
npm -v # 應會印出 "10.9.2"。

然後安裝 playwright 的過程也希望可以全自動，不用人工去點
npm init playwright@latest




我現在需要你幫我測試彰化銀行的網路銀行
https://www.chb.com.tw/chbnib/faces/common/CHBiBMain
請幫我用 playwright 執行瀏覽器

輸入以下資料登入
N126564145
Yong891201
Zxc77410

然後到新台幣帳戶->帳戶總覽->新台幣交易明細的頁面
幫我查詢最近兩個月的交易紀錄
接著會得到類似如下的 curl 
```
curl 'https://www.chb.com.tw/chbnib/faces?IIhfvu=1SQhEAdKBxShl3X7zwsvZvq3mnkQthAv7M9F0dyO9SBsRUXgycr483WrSMPXP3LyZ7h3J7d3o4DEfIECI18MaQJSbJNCjh3t86DCL1AA6bQSpBFayHxDoN0miJO29P4XichgpB5k7WOZjlfogHBDAk3kbsb2ku9JzSMMvQmhL10z5NOOL3IPv6Wfibv3gS1iftiH1GFOr4GDMGVTJ_PM2Dh55K76bZOC7N.7.PISPLU58frhhDEjIRiOu_gNPfd7kZwm_AQyhsPp2zQ3IsL_EeU51lYFzhZ8IMnDiOXuUuBWUSs3rlFraALAdKRWLNqehtzMqc3v_OcHcv6xL_cCvGJqW7b3NIY6QUAUo1nuAKOpAIGeoFP8iox.dpn7gQ0KvFzzNXLrlrvap.UTw8SDqp_WYI81qPEpys4B1ICv9yqRXajlBv3zKi_3kL' \
  -H 'Accept: application/json' \
  -H 'Accept-Language: zh-TW,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-CN;q=0.5' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  -b 'pzqXflQLz5wTSg5443S=jAd1s5ILWJr9u61CEGWiLOILOB6KC3EoHBXnPmZfLgavZS_tR6h4DbxunmohEVPJwjuov1QSYYWrFMbWlsTaSq; CHB_dap_eBANK=********.47873.0000; JSESSIONID_NIB=0000nqQR69GmykqTy9PKM_WtK4j:1b3glohmt; Kbha=cd35dc07-694c-4214-9192-68ade9a649f9; EPSK=302481ec-afb1-4844-8d59-bb5a2c624199; TxnMenuNodeID=TW_TW00001@TW_TW00001; JWT=eyJhbGciOiJFQ0RILUVTK0ExMjhLVyIsImVuYyI6IkExMjhDQkMtSFMyNTYiLCJraWQiOiJyZWNlaXZlcidzIGtleSIsImN0eSI6IkpXVCIsImVwayI6eyJrdHkiOiJFQyIsIngiOiJKTkNOWnhfVmU1OGgzZUY1VHhua05yek5RNkVHSmE3RmZ6UVBETXFPRkJVIiwieSI6IlV3TlNBQWFZdHZqMU1yZHRPV3VIY3ZzcU1JMi01M0NHTVU4b0MtWHRkZkUiLCJjcnYiOiJQLTI1NiJ9fQ.Bz8vfn3Uy4qtXtPoTv2NfP70lPELI2w6W8d1fR6de4erKCl2fZWHJA.DU9XIz9Wz8Du8DAvKb5gtA.o9QmxL6zSoxP2po074epTfmjvKla40DbLuiIKRxYFgzZg_BWSj8f5ThKVocJtvMxmFNUIAO0BVwKumCcRqGEv9BL-MI4FEk2xMrZ51zEPsazePcdD3BuQSX_vBi8La0EeQR4T9C-7R6izrffwxR_W0MwvE9cHK8lh3KFWIJxQmhE7JiiELn00Uv_Bll-8zqYLLdMoaOIXu-_U10nVb41EeVZeA1_u4kC1GO77gixyDkC3ahTzChYSZi4vz45HIdCEF02mX2Z34wZL8PU-4rCL-2jsQIWT-jDqJNo5dI8Q9WaTSRFe8Ddrb9y2l284m2lagIcebc61Kd7DNMrnsfSH2fA0_9sfNmZ8jFn4i6NtAv5hOI1zVUsl-Fpti6BYikoK0-bk-gY1TQ_HJuHW1x5dziNx89JJP0GV04Se6_Z49k22mwuxP3BtHf4V4Ihzjvp5hEDQP6SS8w8f3ZshXKCyFuBAqK_nR9K8j2yoB54AIYmL24SHzr8LsntZs_tMoOluBBjWpnjMkYKzdXa4Km-gy-9II_BEezmj4RK_SvpDGZ7yPyi23rDaTBKRdR-uyAchac62g0fQ9z4u8jO9Xc6DAP6iv-R9GOWaFhCDKqqUVOB1nq7RGgSLd2THgYYEk7p.Uh3kbqWI768mMuRwvPAY-w; pzqXflQLz5wTSg5443T=1SZktAI8zxpkD3i9BwuB5vGAonOVEhaBXMl1CdR.bSHMMUia4cfuQ37xuMcbG3ES57.AH7IAm4bqOIL5w15snQzy9shS_W3h.YQmT41hVuOSew4a_O9fuHGZ6n1VAZMtBDfaJcLS521T0_1uSnzhISkBsqy0WGWuP1n59dPkzFTEqCfPv1qjj8FFnzMlTHiYEyggnCHATKZ8d6cfkja50hbJZFKyo8mymScSRDZEHQQwU7dX2RurljMuRtisPrQTS8PpSTXPWxkwAenSzbD0QHTLZImVkEZA9C3iEG8IMaIxFCqJ1_rdk7VXwxUeuNfrkgT8..S8x.Xtxq7E8BVqB.ybPSG_oS2yuOkG.a6C88BbwCA' \
  -H 'DataCheckSum: 3297324985' \
  -H 'Origin: https://www.chb.com.tw' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://www.chb.com.tw/chbnib/faces/common/TxnPage/tw' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0' \
  -H 'X-Requested-With: XMLHttpRequest' \
  -H 'sec-ch-ua: "Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  --data-raw '{"url":"CHBIB_COMMON/BreadCrumbs","method":"POST","taskID":"none","data":{"txnId":"TW01002"}}'
  ```

但是會得到回應
```
{
    "isSuccess": false,
    "data": "",
    "returnCode": "E004",
    "returnMessage": "無Token資料，無法驗證!!",
    "serverTime": 1749075629729
}
```

我需要想辦法可以利用代理登入的方式來取得交易資料
向樂天銀行模組我就有使用 mitmproxy 來取得一些資料，讓我可以順利呼叫 API 取得交易資料
所以我現在需要你協助我找出彰化銀行網頁上如何得到 token
