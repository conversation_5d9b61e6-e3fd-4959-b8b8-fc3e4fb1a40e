# 🚀 分享我的開源項目：MCP Feedback Enhanced

## 📖 項目背景故事

大家好！我是一名後端工程師，想跟大家分享一個我最近在維護的開源項目。

一開始我只是看到了 [interactive-feedback-mcp](https://github.com/noopstudios/interactive-feedback-mcp) 這個項目，覺得很有趣。但是因為我本身有非常多的項目需要 SSH remote 進行開發，原版只有本地 GUI 介面對我來說不太夠用。

於是我就自己 fork 了這個項目，一邊改一邊調整，加了 Web UI 支援、多語言介面、圖片上傳等功能。突然有一天注意到星星數在增加，搜了一下才知道被分享到了 [這裡](https://linux.do/t/topic/691622)！

沒想到像是發現了新大陸，原來 linux.do 有這麼棒的開發者社群！以後有機會會多多在這裡分享交流。

## 🎯 項目介紹

**MCP Feedback Enhanced** - 一個專為 AI 輔助開發工具設計的人機交互 MCP 服務器

[![GitHub](https://img.shields.io/badge/GitHub-Repository-blue?logo=github)](https://github.com/Minidoracat/mcp-feedback-enhanced)
[![Stars](https://img.shields.io/github/stars/Minidoracat/mcp-feedback-enhanced)](https://github.com/Minidoracat/mcp-feedback-enhanced/stargazers)
[![Forks](https://img.shields.io/github/forks/Minidoracat/mcp-feedback-enhanced)](https://github.com/Minidoracat/mcp-feedback-enhanced/network)

## ✨ 核心特色

- 🖥️ **雙介面系統**：Qt GUI（本地環境）+ Web UI（SSH Remote 環境）
- 🧠 **智能切換**：自動檢測環境並選擇最佳介面
- 🖼️ **圖片支援**：拖拽上傳 + 剪貼板貼上（Ctrl+V）
- 🌏 **多語言**：中英文介面，支援繁簡體
- 🎯 **成本優化**：將多個 AI 工具調用整合為單一回饋請求
- 🔧 **平台支援**：Cursor、Cline、Windsurf、Augment、Trae

## 🚀 快速開始

```bash
# 安裝 uv（如果還沒有的話）
pip install uv

# 快速測試
uvx mcp-feedback-enhanced@latest test
```

## 📊 項目數據

⭐ **480 stars** • 🍴 **30 forks** • 📝 **Python 69.6%** • 🔄 **持續更新**

## 🎮 關於我

除了寫代碼，我個人也是長期在架設免費遊戲伺服器的愛好者，包括：

**長期運營中**：
- 🧟 **Project Zomboid**（4年+）
- 🏗️ **7 Days to Die**

**曾經運營過**：
Rising Storm 系列、ARK、Killing Floor 系列、MORDHAU、Chivalry: Medieval、Unturned、Valheim、Conan、Dark and Light、ARMA 3、V Rising 等等

大多伺服器都運作1年以上，有部分兩年以上。如果有同好歡迎交流！

## 🤝 社群交流

個人 Discord 社群也有挺多人的，歡迎大家來交流：
🔗 **Discord：** https://discord.gg/Gur2V67

## 📝 致謝

特別感謝原作者 [Fábio Ferreira](https://x.com/fabiomlferreira) 的 [原始項目](https://github.com/noopstudios/interactive-feedback-mcp)，如果覺得有用的話也請給原項目一個 ⭐！

---

**🔗 項目地址：** https://github.com/Minidoracat/mcp-feedback-enhanced

歡迎大家試用、提 Issue 或 PR！有任何問題都可以在這裡討論 😊
