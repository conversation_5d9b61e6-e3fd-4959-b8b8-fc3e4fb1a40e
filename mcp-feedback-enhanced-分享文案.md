# 🚀 分享我的开源项目：MCP Feedback Enhanced

## 📖 项目背景故事

大家好！我是一名后端程序猿，想跟大家分享一个我最近在维护的开源项目。
这是我第一次在这发帖，还请多多指教

一开始我只是看到了 [interactive-feedback-mcp](https://github.com/noopstudios/interactive-feedback-mcp) 这个项目，觉得很有趣。但是因为我本身有非常多的项目需要 SSH remote 进行开发，原版只有本地 GUI 接口对我来说不太够用。

于是我就自己 fork 了这个项目，一边改一边调整，加了 Web UI 支持、多语言接口、图片上载等功能。突然有一天注意到星星数在增加，搜了一下才知道被分享到了 [这里](https://linux.do/t/topic/691622)！

没想到像是发现了新大陆，原来 linux.do 有这么棒的开发者社群！以后有机会会多多在这里分享交流。

## 🎯 项目介绍

**MCP Feedback Enhanced** - 一个专为 AI 辅助开发工具设计的人机交互 MCP 服务器

[![GitHub](https://img.shields.io/badge/GitHub-Repository-blue?logo=github)](https://github.com/Minidoracat/mcp-feedback-enhanced)
[![Stars](https://img.shields.io/github/stars/Minidoracat/mcp-feedback-enhanced)](https://github.com/Minidoracat/mcp-feedback-enhanced/stargazers)
[![Forks](https://img.shields.io/github/forks/Minidoracat/mcp-feedback-enhanced)](https://github.com/Minidoracat/mcp-feedback-enhanced/network)
https://github.com/Minidoracat/mcp-feedback-enhanced
## ✨ 内核特色

- 🖥️ **双接口系统**：Qt GUI（本地环境）+ Web UI（SSH Remote 环境）
- 🧠 **智能切换**：自动检测环境并选择最佳接口
- 🖼️ **图片支持**：拖拽上载 + 剪贴板粘贴（Ctrl+V）
- 🌏 **多语言**：中英文接口，支持繁简体
- 🎯 **成本优化**：将多个 AI 工具调用集成为单一回馈请求
- 🔧 **平台支持**：Cursor、Cline、Windsurf、Augment、Trae

## 🚀 快速开始

```bash
# 安装 uv（如果还没有的话）
pip install uv

# 快速测试
uvx mcp-feedback-enhanced@latest test
```

## 🛠️ 配置 MCP
**基本配置**（适合大多数用户）：
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

**进阶配置**（需要自定义环境）：
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "FORCE_WEB": "true",
        "MCP_DEBUG": "false"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

## 🎮 关于我

除了写代码，我个人也是长期在架设免费游戏服务器的爱好者，包括：

**长期运营中**：
- 🧟 **Project Zomboid**（4年+）
- 🏗️ **7 Days to Die**

**曾经运营过**：
Rising Storm 系列、ARK、Killing Floor 系列、MORDHAU、Chivalry: Medieval、Unturned、Valheim、Conan、Dark and Light、ARMA 3、V Rising 等等

大多服务器都运作1年以上，有部分两年以上。如果有同好欢迎交流！

## 🤝 社群交流

个人 Discord 社群也有挺多人的，欢迎大家来交流：
🔗 **Discord：** https://discord.gg/Gur2V67
[![image|385x159](upload://yXezpJhc25EevXfmATKwEklsPLf.png)](https://discord.gg/Gur2V67)


## 📝 致谢

特别感谢原作者 [Fábio Ferreira](https://x.com/fabiomlferreira) 的 [原始项目](https://github.com/noopstudios/interactive-feedback-mcp)，如果觉得有用的话也请给原项目一个 ⭐！

---

**🔗 项目地址：** https://github.com/Minidoracat/mcp-feedback-enhanced

欢迎大家试用、提 Issue 或 PR！有任何问题都可以在这里讨论 😊
